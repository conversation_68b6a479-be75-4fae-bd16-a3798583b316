import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_modal.dart';

/// A reusable card widget for displaying SystemPersona information
///
/// This widget provides a vertical layout with:
/// - Centered persona avatar/image
/// - Persona name and description below the image
/// - Consistent theming and spacing
/// - Tap handling with visual feedback
/// - Support for selection states
/// - Optional video playback on avatar tap
class PersonaCard extends StatelessWidget {
  final models.SystemPersona persona;
  final bool isSelected;
  final VoidCallback? onTap;
  final VoidCallback? onAvatarTap;
  final bool showSelectionIndicator;
  final EdgeInsets? margin;
  final double? width;
  final double? height;

  const PersonaCard({
    super.key,
    required this.persona,
    this.isSelected = false,
    this.onTap,
    this.onAvatarTap,
    this.showSelectionIndicator = false,
    this.margin,
    this.width,
    this.height,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: width,
      height: height,
      margin: margin ?? const EdgeInsets.only(bottom: AppDimensions.spacingM),
      child: Card(
        elevation: AppDimensions.elevationS,
        child: InkWell(
          onTap: onTap,
          borderRadius: AppDimensions.borderRadiusM,
          child: Container(
            padding: const EdgeInsets.all(AppDimensions.spacingM),
            decoration: BoxDecoration(
              borderRadius: AppDimensions.borderRadiusM,
              border: isSelected
                  ? Border.all(color: context.colorScheme.primary, width: 2)
                  : null,
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                // Avatar section with tap handling
                _buildAvatar(context),

                SizedBox(height: AppDimensions.spacingM),

                // Name and description section
                _buildContent(context),

                // Selection indicator if needed
                if (showSelectionIndicator && isSelected) ...[
                  SizedBox(height: AppDimensions.spacingS),
                  _buildSelectionIndicator(context),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildAvatar(BuildContext context) {
    return GestureDetector(
      onTap: onAvatarTap ?? () => _showVideoModal(context),
      child: Container(
        decoration: BoxDecoration(
          shape: BoxShape.circle,
          boxShadow: [
            BoxShadow(
              color: context.colorScheme.shadow.withValues(alpha: 0.1),
              blurRadius: 8,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Stack(
          children: [
            CircleAvatar(
              radius: 40,
              backgroundColor: context.colorScheme.primaryContainer,
              backgroundImage: _getAvatarImage(),
              child: _getAvatarImage() == null
                  ? Icon(
                      AppIcons.profile,
                      size: 40,
                      color: context.colorScheme.onPrimaryContainer,
                    )
                  : null,
            ),
            // Play button overlay
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  shape: BoxShape.circle,
                  color: Colors.black.withValues(alpha: 0.3),
                ),
                child: Icon(
                  Icons.play_circle_outline,
                  size: 32,
                  color: Colors.white.withValues(alpha: 0.9),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showVideoModal(BuildContext context) {
    PersonaVideoModal.show(
      context,
      persona: persona,
      videoPath: 'assets/persona-videos/coach.mp4',
    );
  }

  Widget _buildContent(BuildContext context) {
    return Column(
      children: [
        // Persona name
        Text(
          persona.name,
          style: context.textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
          maxLines: 2,
          overflow: TextOverflow.ellipsis,
        ),

        // Persona description
        if (persona.description != null && persona.description!.isNotEmpty) ...[
          SizedBox(height: AppDimensions.spacingS),
          Text(
            persona.description!,
            style: context.textTheme.bodyMedium?.copyWith(
              color: context.colorScheme.onSurface.withValues(alpha: 0.7),
            ),
            textAlign: TextAlign.center,
            maxLines: 3,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ],
    );
  }

  Widget _buildSelectionIndicator(BuildContext context) {
    return Icon(
      Icons.check_circle,
      color: context.colorScheme.primary,
      size: 24,
    );
  }

  ImageProvider? _getAvatarImage() {
    if (persona.avatarUrl == null || persona.avatarUrl!.isEmpty) {
      return null;
    }

    if (persona.avatarUrl!.startsWith('assets/')) {
      return AssetImage(persona.avatarUrl!);
    } else {
      return NetworkImage(persona.avatarUrl!);
    }
  }
}
