import 'package:flutter/material.dart';
import 'package:chewie/chewie.dart';
import 'package:video_player/video_player.dart';
import 'package:upshift/theme/theme.dart';

/// A video player widget for displaying persona introduction videos
/// 
/// This widget provides:
/// - Video playback using Chewie player
/// - Proper lifecycle management
/// - Loading states and error handling
/// - Fullscreen support
/// - Cross-platform compatibility
class PersonaVideoPlayer extends StatefulWidget {
  final String videoPath;
  final bool autoPlay;
  final bool looping;
  final bool showControls;
  final double? aspectRatio;

  const PersonaVideoPlayer({
    super.key,
    required this.videoPath,
    this.autoPlay = true,
    this.looping = false,
    this.showControls = true,
    this.aspectRatio,
  });

  @override
  State<PersonaVideoPlayer> createState() => _PersonaVideoPlayerState();
}

class _PersonaVideoPlayerState extends State<PersonaVideoPlayer> {
  VideoPlayerController? _videoPlayerController;
  ChewieController? _chewieController;
  bool _isLoading = true;
  String? _errorMessage;

  @override
  void initState() {
    super.initState();
    _initializePlayer();
  }

  @override
  void dispose() {
    _disposeControllers();
    super.dispose();
  }

  Future<void> _initializePlayer() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      // Initialize video player controller
      _videoPlayerController = VideoPlayerController.asset(widget.videoPath);
      await _videoPlayerController!.initialize();

      // Initialize Chewie controller
      _chewieController = ChewieController(
        videoPlayerController: _videoPlayerController!,
        autoPlay: widget.autoPlay,
        looping: widget.looping,
        showControls: widget.showControls,
        aspectRatio: widget.aspectRatio ?? 
            _videoPlayerController!.value.aspectRatio,
        allowFullScreen: true,
        allowMuting: true,
        allowPlaybackSpeedChanging: false,
        showControlsOnInitialize: false,
        materialProgressColors: ChewieProgressColors(
          playedColor: Theme.of(context).colorScheme.primary,
          handleColor: Theme.of(context).colorScheme.primary,
          backgroundColor: Theme.of(context).colorScheme.surfaceContainerHighest,
          bufferedColor: Theme.of(context).colorScheme.primary.withValues(
            alpha: 0.3,
          ),
        ),
        placeholder: _buildPlaceholder(),
        errorBuilder: (context, errorMessage) => _buildError(errorMessage),
      );

      setState(() {
        _isLoading = false;
      });
    } catch (e) {
      setState(() {
        _isLoading = false;
        _errorMessage = 'Failed to load video: $e';
      });
    }
  }

  void _disposeControllers() {
    _chewieController?.dispose();
    _videoPlayerController?.dispose();
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoading();
    }

    if (_errorMessage != null) {
      return _buildError(_errorMessage!);
    }

    if (_chewieController == null) {
      return _buildError('Video player not initialized');
    }

    return Container(
      decoration: BoxDecoration(
        borderRadius: AppDimensions.borderRadiusM,
        color: Colors.black,
      ),
      child: ClipRRect(
        borderRadius: AppDimensions.borderRadiusM,
        child: Chewie(controller: _chewieController!),
      ),
    );
  }

  Widget _buildLoading() {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: AppDimensions.borderRadiusM,
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
      ),
      child: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(
              color: Theme.of(context).colorScheme.primary,
            ),
            SizedBox(height: AppDimensions.spacingM),
            Text(
              'Loading video...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onSurface.withValues(
                  alpha: 0.7,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPlaceholder() {
    return Container(
      color: Colors.black,
      child: Center(
        child: Icon(
          Icons.play_circle_outline,
          size: 64,
          color: Colors.white.withValues(alpha: 0.8),
        ),
      ),
    );
  }

  Widget _buildError(String message) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        borderRadius: AppDimensions.borderRadiusM,
        color: Theme.of(context).colorScheme.errorContainer,
      ),
      child: Center(
        child: Padding(
          padding: const EdgeInsets.all(AppDimensions.spacingM),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                AppIcons.error,
                size: AppDimensions.iconXl,
                color: Theme.of(context).colorScheme.onErrorContainer,
              ),
              SizedBox(height: AppDimensions.spacingM),
              Text(
                'Video Error',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                  fontWeight: FontWeight.w600,
                ),
                textAlign: TextAlign.center,
              ),
              SizedBox(height: AppDimensions.spacingS),
              Text(
                message,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Theme.of(context).colorScheme.onErrorContainer,
                ),
                textAlign: TextAlign.center,
                maxLines: 3,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    );
  }

  /// Play the video
  void play() {
    _chewieController?.play();
  }

  /// Pause the video
  void pause() {
    _chewieController?.pause();
  }

  /// Seek to a specific position
  void seekTo(Duration position) {
    _chewieController?.seekTo(position);
  }

  /// Get current video position
  Duration? get position => _chewieController?.videoPlayerController.value.position;

  /// Get video duration
  Duration? get duration => _chewieController?.videoPlayerController.value.duration;

  /// Check if video is playing
  bool get isPlaying => _chewieController?.isPlaying ?? false;
}
