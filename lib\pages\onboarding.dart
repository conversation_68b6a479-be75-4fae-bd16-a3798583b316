import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import '../models/models.dart';
import '../services/firestore.dart';
import '../services/analytics_service.dart';
import '../services/performance_service.dart';
import '../theme/theme.dart';
import '../widgets/persona_card.dart';
import '../widgets/persona_video_modal.dart';
import 'main_navigation.dart';

class OnboardingPage extends StatefulWidget {
  const OnboardingPage({super.key});

  @override
  State<OnboardingPage> createState() => _OnboardingPageState();
}

class _OnboardingPageState extends State<OnboardingPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  List<SystemPersona> _systemPersonas = [];
  final Set<String> _selectedPersonaIds = {};
  bool _isLoading = false;
  bool _isLoadingPersonas = true;

  @override
  void initState() {
    super.initState();
    _loadSystemPersonas();
  }

  @override
  void dispose() {
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadSystemPersonas() async {
    try {
      final personas = await FirestoreService.getActiveSystemPersonas();
      setState(() {
        _systemPersonas = personas;
        _isLoadingPersonas = false;
      });
    } catch (e) {
      setState(() {
        _isLoadingPersonas = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Failed to load coaches: $e')));
      }
    }
  }

  void _togglePersonaSelection(String? personaId) {
    if (personaId == null) return;

    setState(() {
      if (_selectedPersonaIds.contains(personaId)) {
        _selectedPersonaIds.remove(personaId);
      } else if (_selectedPersonaIds.length < 3) {
        _selectedPersonaIds.add(personaId);
      } else {
        // Show message that only 3 can be selected
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('You can select up to 3 coaches'),
            duration: Duration(seconds: 2),
          ),
        );
      }
    });
  }

  Future<void> _completeOnboarding() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    if (_selectedPersonaIds.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one coach')),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final currentUser = FirebaseAuth.instance.currentUser;
      if (currentUser == null) {
        throw Exception('User not authenticated');
      }

      // Measure onboarding completion performance
      await PerformanceService.instance.measureOnboardingCompletion(() async {
        await FirestoreService.updateUserOnboarding(
          userId: currentUser.uid,
          name: _nameController.text.trim(),
          description: _descriptionController.text.trim().isEmpty
              ? null
              : _descriptionController.text.trim(),
          preferredPersonaIds: _selectedPersonaIds.toList(),
        );

        // Track onboarding completion in analytics
        await AnalyticsService.instance.logOnboardingComplete();

        // Update user properties to reflect onboarded status
        await AnalyticsService.instance.setUserProperty(
          name: 'is_onboarded',
          value: 'true',
        );

        // Track selected personas count
        await AnalyticsService.instance.setUserProperty(
          name: 'preferred_personas_count',
          value: _selectedPersonaIds.length.toString(),
        );

        // Log persona selections
        for (final personaId in _selectedPersonaIds) {
          await AnalyticsService.instance.logPersonaSelected(
            personaId: personaId,
            selectionContext: 'onboarding',
          );
        }
      }, _selectedPersonaIds.length);

      if (mounted) {
        // Navigate to main app
        Navigator.of(context).pushReplacement(
          MaterialPageRoute(builder: (context) => const MainNavigationPage()),
        );
      }
    } catch (e) {
      setState(() {
        _isLoading = false;
      });
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to complete onboarding: $e')),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SafeArea(
        child: SingleChildScrollView(
          padding: AppDimensions.paddingL,
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header Section
                SizedBox(
                  height: AppDimensions.spacingXl + AppDimensions.spacingS,
                ),
                Text('Welcome!', style: AppTypography.onboardingTitle),
                SizedBox(height: AppDimensions.spacingS),
                Text(
                  'Let\'s get you set up. You can modify all of this information later in settings.',
                  style: AppTypography.onboardingSubtitle.copyWith(
                    color: context.colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                SizedBox(
                  height: AppDimensions.spacingXl + AppDimensions.spacingS,
                ),

                // User Information Form
                Text(
                  'Tell us about yourself',
                  style: context.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                SizedBox(height: AppDimensions.spacingM),

                // Name Input
                TextFormField(
                  controller: _nameController,
                  decoration: const InputDecoration(
                    labelText: 'What should we call you?',
                    border: OutlineInputBorder(),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter your name';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),

                // Description Input
                TextFormField(
                  controller: _descriptionController,
                  decoration: const InputDecoration(
                    labelText: 'Tell us about yourself',
                    hintText:
                        'Share details about your age, interests, work, family, hobbies, etc. This helps our AI coaches provide better guidance.',
                    border: OutlineInputBorder(),
                  ),
                  maxLines: 4,
                  textInputAction: TextInputAction.newline,
                ),
                const SizedBox(height: 16),

                // Upload Photo Button (UI only)
                OutlinedButton.icon(
                  onPressed: () {
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('Photo upload coming soon!'),
                      ),
                    );
                  },
                  icon: const Icon(Icons.camera_alt),
                  label: const Text('Upload Photo'),
                ),
                const SizedBox(height: 40),

                // Coach Selection Section
                Text(
                  'Choose Your Preferred Coaches',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.w600),
                ),
                const SizedBox(height: 8),
                Text(
                  'Select up to 3 coaches that resonate with you',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: Theme.of(
                      context,
                    ).colorScheme.onSurface.withValues(alpha: 0.7),
                  ),
                ),
                const SizedBox(height: 16),

                // System Personas List
                if (_isLoadingPersonas)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: CircularProgressIndicator(),
                    ),
                  )
                else if (_systemPersonas.isEmpty)
                  const Center(
                    child: Padding(
                      padding: EdgeInsets.all(32.0),
                      child: Text('No coaches available'),
                    ),
                  )
                else
                  ..._systemPersonas.map(
                    (persona) => _buildPersonaCard(persona),
                  ),

                const SizedBox(height: 40),

                // Complete Onboarding Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _completeOnboarding,
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                    ),
                    child: _isLoading
                        ? const SizedBox(
                            height: 20,
                            width: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        : const Text(
                            'Complete Onboarding',
                            style: TextStyle(fontSize: 16),
                          ),
                  ),
                ),
                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildPersonaCard(SystemPersona persona) {
    final isSelected = _selectedPersonaIds.contains(persona.id);

    // Use horizontal layout for onboarding to maintain existing design
    return Card(
      margin: const EdgeInsets.only(bottom: AppDimensions.spacingM),
      elevation: AppDimensions.elevationS,
      child: InkWell(
        onTap: () => _togglePersonaSelection(persona.id),
        borderRadius: AppDimensions.borderRadiusM,
        child: Container(
          padding: const EdgeInsets.all(AppDimensions.spacingM),
          decoration: BoxDecoration(
            borderRadius: AppDimensions.borderRadiusM,
            border: isSelected
                ? Border.all(color: context.colorScheme.primary, width: 2)
                : null,
          ),
          child: Row(
            children: [
              // Avatar with video playback
              GestureDetector(
                onTap: () => _showPersonaVideo(persona),
                child: Stack(
                  children: [
                    CircleAvatar(
                      radius: 30,
                      backgroundColor: context.colorScheme.primaryContainer,
                      backgroundImage: _getAvatarImage(persona),
                      child: _getAvatarImage(persona) == null
                          ? Icon(
                              AppIcons.profile,
                              size: 30,
                              color: context.colorScheme.onPrimaryContainer,
                            )
                          : null,
                    ),
                    // Play button overlay
                    Positioned.fill(
                      child: Container(
                        decoration: BoxDecoration(
                          shape: BoxShape.circle,
                          color: Colors.black.withValues(alpha: 0.3),
                        ),
                        child: Icon(
                          Icons.play_circle_outline,
                          size: 24,
                          color: Colors.white.withValues(alpha: 0.9),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              SizedBox(width: AppDimensions.spacingM),
              // Content
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      persona.name,
                      style: context.textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    if (persona.description != null &&
                        persona.description!.isNotEmpty) ...[
                      SizedBox(height: AppDimensions.spacingXs),
                      Text(
                        persona.description!,
                        style: context.textTheme.bodyMedium?.copyWith(
                          color: context.colorScheme.onSurface.withValues(
                            alpha: 0.7,
                          ),
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),
                    ],
                  ],
                ),
              ),
              // Selection indicator
              if (isSelected)
                Icon(
                  Icons.check_circle,
                  color: context.colorScheme.primary,
                  size: 24,
                ),
            ],
          ),
        ),
      ),
    );
  }

  void _showPersonaVideo(SystemPersona persona) {
    PersonaVideoModal.show(
      context,
      persona: persona,
      videoPath: 'assets/persona-videos/coach.mp4',
    );
  }

  ImageProvider? _getAvatarImage(SystemPersona persona) {
    if (persona.avatarUrl == null || persona.avatarUrl!.isEmpty) {
      return null;
    }

    if (persona.avatarUrl!.startsWith('assets/')) {
      return AssetImage(persona.avatarUrl!);
    } else {
      return NetworkImage(persona.avatarUrl!);
    }
  }
}
