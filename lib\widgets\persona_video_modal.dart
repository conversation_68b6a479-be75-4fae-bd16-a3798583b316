import 'package:flutter/material.dart';
import 'package:upshift/models/models.dart' as models;
import 'package:upshift/theme/theme.dart';
import 'package:upshift/widgets/persona_video_player.dart';

/// A modal dialog for displaying persona introduction videos
/// 
/// This widget provides:
/// - Full-screen modal presentation
/// - Video player with controls
/// - Persona information display
/// - Proper modal lifecycle management
/// - Responsive design
class PersonaVideoModal extends StatelessWidget {
  final models.SystemPersona persona;
  final String videoPath;

  const PersonaVideoModal({
    super.key,
    required this.persona,
    required this.videoPath,
  });

  /// Show the video modal
  static Future<void> show(
    BuildContext context, {
    required models.SystemPersona persona,
    String videoPath = 'assets/persona-videos/coach.mp4',
  }) {
    return showDialog<void>(
      context: context,
      barrierDismissible: true,
      barrierColor: Colors.black87,
      builder: (context) => PersonaVideoModal(
        persona: persona,
        videoPath: videoPath,
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    final screenSize = MediaQuery.of(context).size;
    final isLandscape = screenSize.width > screenSize.height;
    
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.all(
        isLandscape ? AppDimensions.spacingS : AppDimensions.spacingM,
      ),
      child: Container(
        constraints: BoxConstraints(
          maxWidth: isLandscape ? screenSize.width * 0.9 : 500,
          maxHeight: isLandscape ? screenSize.height * 0.9 : 600,
        ),
        decoration: BoxDecoration(
          color: context.colorScheme.surface,
          borderRadius: AppDimensions.borderRadiusL,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.3),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header with persona info and close button
            _buildHeader(context),
            
            // Video player section
            Flexible(
              child: _buildVideoSection(context),
            ),
            
            // Footer with persona description
            _buildFooter(context),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: context.colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(AppDimensions.radiusL),
          topRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: Row(
        children: [
          // Persona avatar
          CircleAvatar(
            radius: 20,
            backgroundColor: context.colorScheme.primaryContainer,
            backgroundImage: _getAvatarImage(),
            child: _getAvatarImage() == null
                ? Icon(
                    AppIcons.profile,
                    size: 20,
                    color: context.colorScheme.onPrimaryContainer,
                  )
                : null,
          ),
          
          SizedBox(width: AppDimensions.spacingM),
          
          // Persona name
          Expanded(
            child: Text(
              persona.name,
              style: context.textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
            ),
          ),
          
          // Close button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Icon(
              Icons.close,
              color: context.colorScheme.onSurface,
            ),
            tooltip: 'Close video',
          ),
        ],
      ),
    );
  }

  Widget _buildVideoSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      child: PersonaVideoPlayer(
        videoPath: videoPath,
        autoPlay: true,
        looping: false,
        showControls: true,
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    if (persona.description == null || persona.description!.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(AppDimensions.spacingM),
      decoration: BoxDecoration(
        color: context.colorScheme.surfaceContainerHighest,
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppDimensions.radiusL),
          bottomRight: Radius.circular(AppDimensions.radiusL),
        ),
      ),
      child: Text(
        persona.description!,
        style: context.textTheme.bodyMedium?.copyWith(
          color: context.colorScheme.onSurface.withValues(alpha: 0.8),
        ),
        textAlign: TextAlign.center,
        maxLines: 3,
        overflow: TextOverflow.ellipsis,
      ),
    );
  }

  ImageProvider? _getAvatarImage() {
    if (persona.avatarUrl == null || persona.avatarUrl!.isEmpty) {
      return null;
    }

    if (persona.avatarUrl!.startsWith('assets/')) {
      return AssetImage(persona.avatarUrl!);
    } else {
      return NetworkImage(persona.avatarUrl!);
    }
  }
}
